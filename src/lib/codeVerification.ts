// Code verification and error fixing system
export interface VerificationResult {
  isValid: boolean;
  errors: VerificationError[];
  fixedCode: string;
  warnings: string[];
}

export interface VerificationError {
  type: string;
  message: string;
  line?: number;
  fixed: boolean;
  originalCode?: string;
  fixedCode?: string;
}

export interface VerificationProgress {
  step: string;
  progress: number;
  message: string;
  errors?: VerificationError[];
}

// Known error patterns and their fixes
const ERROR_PATTERNS = {
  // Completely wrong imports - using non-existent google_adk
  WRONG_IMPORTS: {
    pattern: /from google_adk import|import.*google_adk|from mcp_toolset import|class.*Agent\):/,
    message: "Using wrong imports - should use google.adk",
    fix: (code: string) => {
      // If code has completely wrong structure, replace with proper template
      if (code.includes('from google_adk import') || code.includes('class DocQueryAgent(Agent)')) {
        return generateProperMCPTemplate();
      }
      return code;
    }
  },

  // Wrong class structure
  WRONG_CLASS_STRUCTURE: {
    pattern: /class\s+\w+\s*\(\s*Agent\s*\):/,
    message: "Using wrong class structure - should use LlmAgent directly",
    fix: (code: string) => {
      if (code.includes('class') && code.includes('Agent')) {
        return generateProperMCPTemplate();
      }
      return code;
    }
  },

  // LlmAgent constructor errors
  LLMAGENT_WRONG_PARAMS: {
    pattern: /LlmAgent\s*\(\s*(?:.*?(?:instructions|toolset|session_service).*?)\)/s,
    message: "LlmAgent constructor has wrong parameter names",
    fix: (code: string) => {
      // Fix parameter names and order: name, model, description, instruction, tools
      return code.replace(
        /LlmAgent\s*\(\s*([\s\S]*?)\)/,
        (match, params) => {
          const lines = params.split('\n').map(line => line.trim()).filter(line => line);
          const fixedParams = [];
          
          // Extract values
          let name = '', model = '', description = '', instruction = '', tools = '';
          
          for (const line of lines) {
            if (line.includes('name=') || line.startsWith('"') || line.startsWith("'")) {
              name = line.replace(/name\s*=\s*/, '').replace(/,$/, '');
            } else if (line.includes('model=')) {
              model = line.replace(/model\s*=\s*/, '').replace(/,$/, '');
            } else if (line.includes('description=')) {
              description = line.replace(/description\s*=\s*/, '').replace(/,$/, '');
            } else if (line.includes('instruction=') || line.includes('instructions=')) {
              instruction = line.replace(/instructions?\s*=\s*/, '').replace(/,$/, '');
            } else if (line.includes('tools=') || line.includes('toolset=')) {
              tools = line.replace(/toolset?\s*=\s*/, '').replace(/,$/, '');
            }
          }
          
          // Ensure model is set
          if (!model) model = '"gemini-2.0-flash"';
          
          return `LlmAgent(
    name=${name || '"agent"'},
    model=${model},
    description=${description || '"AI agent"'},
    instruction=${instruction || '"You are a helpful assistant"'},
    tools=${tools || '[]'}
)`;
        }
      );
    }
  },

  // Runner missing app_name
  RUNNER_MISSING_APP_NAME: {
    pattern: /Runner\s*\(\s*agent\s*=\s*[^,]+,\s*session_service\s*=\s*[^,)]+\s*\)/,
    message: "Runner constructor missing required app_name parameter",
    fix: (code: string) => {
      return code.replace(
        /Runner\s*\(\s*(agent\s*=\s*[^,]+),\s*(session_service\s*=\s*[^,)]+)\s*\)/,
        'Runner($1, $2, app_name="agent")'
      );
    }
  },

  // Missing model in LlmAgent
  MISSING_MODEL: {
    pattern: /LlmAgent\s*\([^)]*\)/s,
    message: "LlmAgent missing model parameter",
    fix: (code: string) => {
      if (!code.includes('model=')) {
        return code.replace(
          /(LlmAgent\s*\(\s*(?:name\s*=\s*[^,]+,?\s*)?)/,
          '$1\n    model="gemini-2.0-flash",'
        );
      }
      return code;
    }
  },

  // Wrong import in __init__.py
  WRONG_INIT_IMPORT: {
    pattern: /from\s+\.\s+import\s+agent/,
    message: "Wrong import in __init__.py - should import root_agent instance",
    fix: (code: string) => {
      return code.replace(
        /from\s+\.\s+import\s+agent/,
        'from .agent import root_agent'
      );
    }
  },

  // Missing --key in MCP args
  MISSING_MCP_KEY: {
    pattern: /args\s*=\s*\[[^\]]*\]/,
    message: "MCP args missing --key parameter",
    fix: (code: string) => {
      return code.replace(
        /args\s*=\s*(\[[^\]]*\])/,
        (match, argsArray) => {
          if (!argsArray.includes('--key')) {
            const cleanArgs = argsArray.slice(1, -1); // Remove brackets
            return `args=[${cleanArgs}, "--key", smithery_api_key]`;
          }
          return match;
        }
      );
    }
  },

  // Missing SMITHERY_API_KEY in env
  MISSING_SMITHERY_ENV: {
    pattern: /env\s*=\s*\{[^}]*\}/,
    message: "Environment variables missing SMITHERY_API_KEY",
    fix: (code: string) => {
      return code.replace(
        /env\s*=\s*(\{[^}]*\})/,
        (match, envObj) => {
          if (!envObj.includes('SMITHERY_API_KEY')) {
            const cleanEnv = envObj.slice(1, -1); // Remove braces
            return `env={${cleanEnv}, "SMITHERY_API_KEY": smithery_api_key}`;
          }
          return match;
        }
      );
    }
  },

  // Using run_forever instead of run_async
  WRONG_RUN_METHOD: {
    pattern: /asyncio\.run\(runner\.run_forever\(\)\)/,
    message: "Using deprecated run_forever method",
    fix: (code: string) => {
      // Replace with proper async pattern
      const asyncPattern = `async def main():
    # Create a session
    user_id = "user"
    session = session_service.create_session(state={}, app_name="agent", user_id=user_id)
    session_id = session.id

    # Create an initial message (Content object)
    from google.genai import types
    new_message = types.Content(
        role="user",
        parts=[types.Part(text="Hello, agent!")]
    )

    # Run the agent
    async for event in runner.run_async(
        user_id=user_id,
        session_id=session_id,
        new_message=new_message
    ):
        print(event)

if __name__ == "__main__":
    asyncio.run(main())`;
      
      return code.replace(
        /if __name__ == "__main__":\s*asyncio\.run\(runner\.run_forever\(\)\)/s,
        asyncPattern
      );
    }
  },

  // Missing google.genai.types import for MCP agents
  MISSING_TYPES_IMPORT: {
    pattern: /from google\.adk\.agents import LlmAgent/,
    message: "Missing google.genai.types import for MCP agents",
    fix: (code: string) => {
      if (code.includes('LlmAgent') && code.includes('run_async') && !code.includes('from google.genai import types')) {
        return code.replace(
          /(from google\.adk\.agents import LlmAgent)/,
          '$1\nfrom google.genai import types  # For Content/Part'
        );
      }
      return code;
    }
  }
};

// Main verification function
export async function verifyAndFixCode(
  code: string,
  onProgress?: (progress: VerificationProgress) => void
): Promise<VerificationResult> {
  const errors: VerificationError[] = [];
  const warnings: string[] = [];
  let fixedCode = code;
  let stepCount = 0;
  const totalSteps = Object.keys(ERROR_PATTERNS).length + 2;

  // Step 1: Initial validation
  stepCount++;
  onProgress?.({
    step: "initial_validation",
    progress: (stepCount / totalSteps) * 100,
    message: "Starting code verification..."
  });

  // Step 2: Check each error pattern
  for (const [errorType, pattern] of Object.entries(ERROR_PATTERNS)) {
    stepCount++;
    onProgress?.({
      step: errorType.toLowerCase(),
      progress: (stepCount / totalSteps) * 100,
      message: `Checking for ${pattern.message}...`
    });

    if (pattern.pattern.test(fixedCode)) {
      const originalCode = fixedCode;
      fixedCode = pattern.fix(fixedCode);
      
      errors.push({
        type: errorType,
        message: pattern.message,
        fixed: fixedCode !== originalCode,
        originalCode: originalCode,
        fixedCode: fixedCode
      });
    }
  }

  // Step 3: Final validation
  stepCount++;
  onProgress?.({
    step: "final_validation",
    progress: 100,
    message: "Verification complete!",
    errors: errors
  });

  // Additional checks
  if (!fixedCode.includes('__all__ = ["root_agent"]')) {
    warnings.push("Missing __all__ export declaration");
    fixedCode += '\n\n__all__ = ["root_agent"]';
  }

  return {
    isValid: errors.length === 0,
    errors,
    fixedCode,
    warnings
  };
}

// Quick validation for specific error types
export function hasKnownErrors(code: string): boolean {
  return Object.values(ERROR_PATTERNS).some(pattern => pattern.pattern.test(code));
}

// Generate proper MCP template when code is completely wrong
function generateProperMCPTemplate(): string {
  return `from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters
from google.genai import types  # For Content/Part
import asyncio
import os

# Set the Smithery API key from environment variable
smithery_api_key = os.getenv("SMITHERY_API_KEY")
if not smithery_api_key:
    raise ValueError("SMITHERY_API_KEY environment variable is not set")

# MCP toolset configuration
toolset = MCPToolset(
    connection_params=StdioServerParameters(
        command="npx",
        args=["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp", "--key", smithery_api_key],
        env={"NODE_OPTIONS": "--no-warnings --experimental-fetch", "SMITHERY_API_KEY": smithery_api_key}
    )
)

# LlmAgent with MCP tools - CORRECT PARAMETER ORDER
root_agent = LlmAgent(
    name="DocQueryAgent",
    model="gemini-2.0-flash",
    description="An LlmAgent that handles user queries about documentation and uses MCP tool to update context and retrieve information.",
    instruction="You are an agent that can use Smithery MCP to perform operations. Use the Smithery MCP tool to interact with external systems and APIs.",
    tools=[toolset]
)

# Session service and runner setup - MUST INCLUDE app_name
session_service = InMemorySessionService()
runner = Runner(agent=root_agent, session_service=session_service, app_name="DocQueryAgent")

async def main():
    # Create a session
    user_id = "user"
    session = session_service.create_session(state={}, app_name="DocQueryAgent", user_id=user_id)
    session_id = session.id

    # Create an initial message (Content object)
    new_message = types.Content(
        role="user",
        parts=[types.Part(text="Hello, agent!")]
    )

    # Run the agent
    async for event in runner.run_async(
        user_id=user_id,
        session_id=session_id,
        new_message=new_message
    ):
        print(event)

if __name__ == "__main__":
    asyncio.run(main())

__all__ = ["root_agent"]`;
}

// Generate proper __init__.py content
export function generateInitPy(): string {
  return `from .agent import root_agent

__all__ = ["root_agent"]`;
}
