// Test file for verification system
import { verifyAndFixCode } from './codeVerification';

// Test code with known errors
const testCodeWithErrors = `from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters

# MCP toolset configuration
toolset = MCPToolset(
    connection_params=StdioServerParameters(
        command="npx",
        args=["-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp"],
        env={"NODE_OPTIONS": "--no-warnings --experimental-fetch"}
    )
)

# LlmAgent with wrong parameters
root_agent = LlmAgent(
    model="gemini-2.0-flash",
    instructions="You are an agent",
    description="Test agent",
    toolset=toolset,
    session_service=session_service
)

# Runner without app_name
session_service = InMemorySessionService()
runner = Runner(agent=root_agent, session_service=session_service)

if __name__ == "__main__":
    asyncio.run(runner.run_forever())`;

// Test the verification system
export async function testVerification() {
  console.log('Testing verification system...');
  
  const result = await verifyAndFixCode(testCodeWithErrors, (progress) => {
    console.log(`Progress: ${progress.progress}% - ${progress.message}`);
  });
  
  console.log('Verification Result:', {
    isValid: result.isValid,
    errorsFound: result.errors.length,
    warnings: result.warnings.length
  });
  
  console.log('Errors fixed:');
  result.errors.forEach(error => {
    console.log(`- ${error.type}: ${error.message} (Fixed: ${error.fixed})`);
  });
  
  console.log('Fixed code:');
  console.log(result.fixedCode);
  
  return result;
}

// Expected fixed code should have:
// 1. Correct LlmAgent parameter order: name, model, description, instruction, tools
// 2. Runner with app_name parameter
// 3. MCP args with --key parameter
// 4. Environment variables with SMITHERY_API_KEY
// 5. Proper async pattern instead of run_forever
