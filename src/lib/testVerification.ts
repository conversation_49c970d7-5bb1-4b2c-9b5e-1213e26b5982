// Test file for verification system
import { verifyAndFixCode } from './codeVerification';

// Test code with the exact errors you encountered
const testCodeWithErrors = `import json
from google_adk import Agent, Tool, Model, MCPClient, MCPTool
from mcp_toolset import StdioServerParameters

class DocQueryAgent(Agent):
    def __init__(self):
        super().__init__(label="DocQueryAgent", description="An LlmAgent that handles user queries about documentation and uses MCP tool to update context and retrieve information.")
        self.mcp_client = MCPClient(label="Smithery Client: context7-mcp")
        self.mcp_tool = MCPTool(label="Smithery MCP: @upstash/context7-mcp")
        self.model = Model(label="Gemini 2.0 Flash", description="Gemini 2.0 Flash model used by the agent for processing queries.")

    def handle_query(self, query):
        # Use MCP tool to update context and retrieve information
        context_update = self.mcp_tool.update_context(query)
        response = self.model.process_query(context_update)
        return response

def main():
    agent = DocQueryAgent()
    server_params = StdioServerParameters()

    # Start the agent with MCP integration
    agent.start(server_params)

if __name__ == "__main__":
    main()

__all__ = ["root_agent"]`;

// Test the verification system
export async function testVerification() {
  console.log('Testing verification system...');
  
  const result = await verifyAndFixCode(testCodeWithErrors, (progress) => {
    console.log(`Progress: ${progress.progress}% - ${progress.message}`);
  });
  
  console.log('Verification Result:', {
    isValid: result.isValid,
    errorsFound: result.errors.length,
    warnings: result.warnings.length
  });
  
  console.log('Errors fixed:');
  result.errors.forEach(error => {
    console.log(`- ${error.type}: ${error.message} (Fixed: ${error.fixed})`);
  });
  
  console.log('Fixed code:');
  console.log(result.fixedCode);
  
  return result;
}

// Test code with duplicate tools parameter error
const testCodeWithDuplicateTools = `from google.adk.agents import LlmAgent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset

toolset = MCPToolset(...)

# Wrong: duplicate tools parameter
root_agent = LlmAgent(
    name="test",
    model="gemini-2.0-flash",
    description="test",
    instruction="test",
    tools=tools=[toolset]  # ERROR: duplicate tools=
)`;

// Test the duplicate tools error specifically
export async function testDuplicateToolsError() {
  console.log('Testing duplicate tools parameter error...');

  const result = await verifyAndFixCode(testCodeWithDuplicateTools);

  console.log('Should fix tools=tools=[toolset] to tools=[toolset]');
  console.log('Fixed code contains duplicate tools=:', result.fixedCode.includes('tools=tools='));
  console.log('Fixed code:', result.fixedCode);

  return result;
}

// Expected fixed code should have:
// 1. Correct LlmAgent parameter order: name, model, description, instruction, tools
// 2. Runner with app_name parameter
// 3. MCP args with --key parameter
// 4. Environment variables with SMITHERY_API_KEY
// 5. Proper async pattern instead of run_forever
// 6. No duplicate tools= parameter (tools=[toolset] not tools=tools=[toolset])
