import type { <PERSON><PERSON>, <PERSON> } from '@xyflow/react';
import type { BaseNodeData } from '@/components/nodes/BaseNode';

export interface MCPConfig {
  enabled: boolean;
  type: string;
  command: string;
  args: string[];
  envVars: { [key: string]: string };
}

// Generate MCP code for agents
export function generateMCPCode(nodes: Node<BaseNodeData>[], mcpConfig: MCPConfig): string {
  const agentNode = nodes.find(n => n.data.type === 'agent');
  if (!agentNode) {
    return generateDefaultSearchAgentCode();
  }

  const agentName = agentNode.data.label?.toLowerCase().replace(/\s+/g, '_') || 'mcp_agent';
  const agentDescription = agentNode.data.description || 'MCP-enabled agent';
  const agentInstruction = agentNode.data.instruction || agentNode.data.prompt ||
    "You are a helpful assistant that can use MCP tools to assist users.";

  // Extract MCP package name from args for better naming
  const mcpPackage = mcpConfig.args.find(arg => arg.startsWith('@')) || '@smithery/mcp-example';
  const packageName = mcpPackage.split('/').pop() || 'mcp-example';

  console.log('Generating MCP code with config:', {
    command: mcpConfig.command,
    args: mcpConfig.args,
    envVars: mcpConfig.envVars,
    mcpPackage
  });

  return `"""${agentName} MCP Agent"""
import os
import asyncio
import logging
from dotenv import load_dotenv
from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("${agentName}")

# Load environment variables
load_dotenv()

# Retrieve Smithery API key from environment variable
smithery_api_key = os.getenv("SMITHERY_API_KEY")
if not smithery_api_key:
    raise ValueError("SMITHERY_API_KEY environment variable is not set")

# MCP toolset configuration for ${packageName}
toolset = MCPToolset(
    connection_params=StdioServerParameters(
        command="${mcpConfig.command}",
        args=${JSON.stringify(mcpConfig.args).replace('"smithery_api_key"', 'smithery_api_key').replace(/"\*\*\*.*?\*\*\*"/g, 'smithery_api_key')},
        env=${JSON.stringify(mcpConfig.envVars)}
    )
)

# LlmAgent with MCP tools
root_agent = LlmAgent(
    model="gemini-2.0-flash",
    name="${agentName}",
    description="${agentDescription}",
    instruction="${agentInstruction}",
    tools=[toolset]
)

# Session service and runner setup
session_service = InMemorySessionService()
runner = Runner(agent=root_agent, session_service=session_service)

__all__ = ["root_agent"]

# Run the agent
if __name__ == "__main__":
    asyncio.run(runner.run_forever())`;
}

// Generate default search agent code
export function generateDefaultSearchAgentCode(): string {
  return `"""Default Search Agent"""
import os
import logging
from dotenv import load_dotenv
from google.adk.agents import Agent
from google.adk.tools import google_search

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("search_agent")

# Load environment variables
load_dotenv()

# Create the agent
root_agent = Agent(
    model="gemini-2.0-flash",
    name="search_agent",
    description="Agent that performs web searches and provides accurate information",
    instruction="Use Google Search to find accurate and up-to-date information. Always cite your sources.",
    tools=[google_search]
)

__all__ = ["root_agent"]

if __name__ == "__main__":
    try:
        response = root_agent.chat("What are the latest developments in AI?")
        print("Agent Response:", response)
    except Exception as e:
        logger.error(f"Error running agent: {e}")
        raise`;
}

// Helper function to detect MCP code
export function isMcpCode(code: string): boolean {
  const mcpIndicators = [
    'MCPToolset',
    'StdioServerParameters',
    'LlmAgent',
    'Runner',
    'InMemorySessionService'
  ];

  return mcpIndicators.some(indicator => code.includes(indicator));
}

// Generate fallback MCP code
export function generateFallbackMcpCode(): string {
  const defaultConfig: MCPConfig = {
    enabled: true,
    type: 'smithery',
    command: 'npx',
    args: ['-y', '@smithery/cli@latest', 'run', '@smithery/mcp-example', '--key', 'smithery_api_key'],
    envVars: { 'NODE_OPTIONS': '--no-warnings --experimental-fetch' }
  };

  return generateMCPCode([], defaultConfig);
}

// Generate ADK code (compatibility function)
export function generateADKCode(nodes: Node<BaseNodeData>[], _edges: Edge[]): string {
  // Check if there are MCP nodes to determine if we should use MCP
  const hasMcpNodes = nodes.some(node =>
    node.data.type === 'mcp-client' ||
    node.data.type === 'mcp-server' ||
    node.data.type === 'mcp-tool'
  );

  if (hasMcpNodes) {
    const defaultConfig: MCPConfig = {
      enabled: true,
      type: 'smithery',
      command: 'npx',
      args: ['-y', '@smithery/cli@latest', 'run', '@smithery/mcp-example', '--key', 'smithery_api_key'],
      envVars: { 'NODE_OPTIONS': '--no-warnings --experimental-fetch' }
    };
    return generateMCPCode(nodes, defaultConfig);
  }

  return generateDefaultSearchAgentCode();
}

// Generate code using OpenAI/OpenRouter API based on node data
export async function generateCodeWithAI(
  nodes: Node<BaseNodeData>[],
  _edges: Edge[],
  mcpEnabled: boolean = true,
  apiKey?: string
): Promise<string> {
  const OPENROUTER_API_BASE = 'https://openrouter.ai/api/v1';

  if (!apiKey) {
    console.warn('No API key provided, falling back to local generation');
    return generateADKCode(nodes, _edges);
  }

  // Prepare node data for the AI prompt
  const nodeData = nodes.map(node => ({
    id: node.id,
    type: node.data.type,
    label: node.data.label,
    description: node.data.description || '',
    instruction: node.data.instruction || '',
    prompt: node.data.prompt || '',
    modelType: node.data.modelType || ''
  }));

  // Find agent nodes to get main configuration
  const agentNodes = nodes.filter(n => n.data.type === 'agent');
  const mcpNodes = nodes.filter(n =>
    n.data.type === 'mcp-client' ||
    n.data.type === 'mcp-server' ||
    n.data.type === 'mcp-tool'
  );

  const systemPrompt = mcpEnabled ?
    `You are an expert Python developer specializing in Google ADK with MCP integration. Generate clean, production-ready Python code for an agent based on the provided node configuration. Use MCPToolset with StdioServerParameters for MCP integration.` :
    `You are an expert Python developer specializing in Google ADK. Generate clean, production-ready Python code for an agent based on the provided node configuration. Use google_search tool for search capabilities.`;

  const userPrompt = `Generate a Google ADK agent based on this flow:

Nodes: ${JSON.stringify(nodeData, null, 2)}

Agent: ${agentNodes.length > 0 ? `${agentNodes[0].data.label || 'search_agent'} - ${agentNodes[0].data.description || 'AI agent'}` : 'Default search agent'}

MCP: ${mcpEnabled ? `Enabled (${mcpNodes.length} MCP nodes)` : 'Disabled'}

Return ONLY Python code, no explanations.`;

  try {
    const response = await fetch(`${OPENROUTER_API_BASE}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'HTTP-Referer': typeof window !== 'undefined' ? window.location.origin : 'https://agent-flow-builder.com',
        'X-Title': 'Agent Flow Builder'
      },
      body: JSON.stringify({
        model: 'openai/gpt-4o-mini',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.2,
        max_tokens: 3000
      })
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const result = await response.json();
    const generatedCode = result.choices[0]?.message?.content || '';

    // Clean the generated code
    const cleanedCode = generatedCode.replace(/```python\n/g, '').replace(/```\n?/g, '').trim();

    // Validate the generated code
    if (!cleanedCode || cleanedCode.length < 100) {
      throw new Error('Generated code too short');
    }

    return cleanedCode;
  } catch (error) {
    console.error('Error calling OpenRouter API:', error);
    // Fall back to local generation
    return generateADKCode(nodes, _edges);
  }
}