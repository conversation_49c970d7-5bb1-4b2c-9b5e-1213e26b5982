"""SMITHERY MCP Agent"""
import asyncio
import logging
import sys
import os
from contextlib import AsyncExitStack
from dotenv import load_dotenv

from google import genai
from google.genai import types
from google.adk.agents import LlmAgent
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters

# Global state
_tools = None
_exit_stack = None

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger("time_management_agent")

# Load environment variables from both locations
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '.env'))


async def get_tools_async():
    """Gets tools from the SMITHERY MCP Server."""
    global _tools, _exit_stack
    
    # Get Smithery API key from environment
    smithery_api_key = os.getenv('SMITHERY_API_KEY')
    if not smithery_api_key:
        raise ValueError("SMITHERY_API_KEY environment variable is not set")
    
    _exit_stack = AsyncExitStack()
    await _exit_stack.__aenter__()

    try:
        toolset = MCPToolset(
            connection_params=StdioServerParameters(
                command='npx',
                args=[
                    "-y", 
                    "@smithery/cli@latest", 
                    "run", 
                    "@yokingma/time-mcp",
                    "--key",
                    smithery_api_key
                ],
                env={
                    "NODE_OPTIONS": "--no-warnings --experimental-fetch"
                }
            )
        )
        # Register cleanup with exit stack
        await _exit_stack.push_async_callback(toolset.aclose)

        _tools = [toolset]  # Store tools in global state
        return _tools
    except Exception as e:
        logger.error(f"Failed to load MCP tools: {e}", exc_info=True)
        await _exit_stack.aclose()
        _exit_stack = None
        raise


# Try to initialize tools at import time (eager loading)
try:
    asyncio.run(get_tools_async())
    logger.info("Initialized SMITHERY MCP tools for the agent.")
except Exception as e:
    logger.warning(f"Warning: Failed to initialize SMITHERY tools: {e}")
    logger.info("Agent will attempt to initialize tools when first used.")


class Time_management_agentAgent(LlmAgent):
    def __init__(self):
        super().__init__(
            model="gemini-2.0-flash",
            name="time_management_agent",
            description="An LlmAgent that manages and handles time-related queries using MCP integration.",
            instruction="""You are a helpful assistant that can perform time-related operations.

Available functions:
- get_current_time(timezone="IANA timezone name") - Get current time in a specific timezone
- convert_time(source_timezone="source IANA timezone", time="HH:MM", target_timezone="target IANA timezone") - Convert time between timezones

IMPORTANT RULES:
1. Always use valid IANA timezone names (e.g., 'America/New_York', 'Europe/London', 'Asia/Tokyo')
2. Use 24-hour format for time (HH:MM)
3. Handle timezone conversions carefully
4. Provide clear explanations for time calculations""",
            tools=_tools or []
        )


# Configure Google AI client
GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')
if not GOOGLE_API_KEY:
    logger.warning("GOOGLE_API_KEY environment variable is not set. Some features may not work properly.")

# Create the root agent instance
root_agent = Time_management_agentAgent()


async def main():
    """Run the agent."""
    global _tools, _exit_stack
    try:
        # Initialize Google AI client if API key is available
        genai_client = None
        if GOOGLE_API_KEY:
            genai_client = genai.Client(api_key=GOOGLE_API_KEY)
        
        # Ensure tools are loaded
        if _tools is None:
            logger.info("Loading MCP tools...")
            _tools = await get_tools_async()
            root_agent.tools = _tools
            logger.info(f"Loaded MCP tools: {[tool.name for tool in _tools]}")

        # Set up session service and runner
        session_service = InMemorySessionService()
        runner = Runner(
            app_name='time_management_app',
            agent=root_agent, 
            session_service=session_service,
            inputs={'client': genai_client} if genai_client else {}
        )

        # Option 1: Run a single test query
        if len(sys.argv) > 1 and sys.argv[1] == "--test":
            logger.info("Running test query: What is the current time in Tokyo?")
            session = session_service.create_session(
                state={}, app_name='time_management_app', user_id='test_user'
            )
            async for event in runner.run_async(
                session_id=session.id,
                user_id=session.user_id,
                new_message=types.Content(
                    role='user',
                    parts=[types.Part(text="What is the current time in Tokyo?")]
                )
            ):
                print(event)
        # Option 2: Run interactive mode
        else:
            logger.info("Starting agent runner in interactive mode. Press Ctrl+C to exit.")
            await runner.run_forever()

    except asyncio.CancelledError:
        logger.info("Agent runner cancelled, shutting down.")
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}", exc_info=True)
    finally:
        if _exit_stack is not None:
            await _exit_stack.aclose()
            _exit_stack = None
        logger.info("Cleanup complete.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, exiting.")
        sys.exit(0)
